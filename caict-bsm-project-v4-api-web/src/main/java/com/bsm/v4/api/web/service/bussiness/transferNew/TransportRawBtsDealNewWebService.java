package com.bsm.v4.api.web.service.bussiness.transferNew;

import com.bsm.v4.domain.security.service.business.transferNew.TransportRawBtsDealNewService;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * CSV 原始数据存储表WebService
 */
@Service
public class TransportRawBtsDealNewWebService extends BasicWebService {

    @Autowired
    private TransportRawBtsDealNewService transportRawBtsDealNewService;
    
}
