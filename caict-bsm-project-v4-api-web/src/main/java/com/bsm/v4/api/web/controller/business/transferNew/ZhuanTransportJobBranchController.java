package com.bsm.v4.api.web.controller.business.transferNew;

import com.bsm.v4.api.web.service.bussiness.transferNew.ZhuanTransportJobBranchWebService;
import com.caictframework.data.controller.BasicController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 按地市拆分的子任务表Controller
 */
@RestController
@RequestMapping(value = "/apiWeb/transferNew/zhuanTransportJobBranch")
@Api(value = "按地市拆分的子任务管理接口", tags = "按地市拆分的子任务管理接口")
public class ZhuanTransportJobBranchController extends BasicController {

    @Autowired
    private ZhuanTransportJobBranchWebService zhuanTransportJobBranchWebService;
    
}
