package com.bsm.v4.api.web.controller.business.transferNew;

import com.bsm.v4.api.web.service.bussiness.transferNew.TransportFieldMappingLogWebService;
import com.caictframework.data.controller.BasicController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 字段映射记录表Controller（用于字段标准化跟踪）
 */
@RestController
@RequestMapping(value = "/apiWeb/transferNew/transportFieldMappingLog")
@Api(value = "web端字段映射记录管理接口", tags = "字段映射记录管理接口")
public class TransportFieldMappingLogController extends BasicController {

    @Autowired
    private TransportFieldMappingLogWebService transportFieldMappingLogWebService;
    
}
