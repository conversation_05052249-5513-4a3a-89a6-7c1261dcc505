package com.bsm.v4.api.web.controller.business.transferNew;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.transferNew.TransportJobNewWebService;
import com.bsm.v4.api.web.service.security.AuthWebService;
import com.caictframework.data.controller.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 传输任务表Controller
 */
@RestController
@RequestMapping(value = "/apiWeb/transferNew/zhuanTransportJob")
@Api(value = "web端传输任务管理接口", tags = "web端传输任务管理接口")
public class TransportJobNewController extends BasicController {

    @Autowired
    private TransportJobNewWebService transportJobNewWebService;

    @Autowired
    private AuthWebService authWebService;

    @ApiOperation(value = "上传CSV文件", notes = "支持上传CSV文件，校验后缀名并自动重命名避免覆盖")
    @PostMapping(value = "/uploadCsv")
    public JSONObject uploadCsv(
            @ApiParam(value = "CSV文件", required = true) @RequestParam("file") MultipartFile file) {
        return this.basicReturnJson(file, TransportJobNewWebService.class,
                (fileParam, service) -> service.uploadCsvFile(fileParam));
    }

}
