package com.bsm.v4.api.web;

import com.github.tobato.fastdfs.FdfsClientConfig;
import org.apache.catalina.connector.Connector;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.boot.web.servlet.server.ServletWebServerFactory;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.*;
import org.springframework.data.elasticsearch.repository.config.EnableElasticsearchRepositories;
import org.springframework.jmx.support.RegistrationPolicy;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.servlet.MultipartConfigElement;

/**
 * Created by dengsy on 2023-02-10.
 */
@SpringBootApplication
@ComponentScan({"com.bsm.v4",
                "com.caictframework"})
@MapperScan({"com.bsm.v4.domain.security.mapper"})
@Configuration
@EnableTransactionManagement
@EnableMBeanExport(registration = RegistrationPolicy.IGNORE_EXISTING) //使用FastFDS时解决JMX重复注册bean的问题
@Import(FdfsClientConfig.class) //使用FastFDS时可以拥有带有连接池的FastDFS Java客户端
@EnableElasticsearchRepositories(basePackages = "com.bsm.v4")

public class WebApplication extends SpringBootServletInitializer {

    @Value("${caict.myFilePathTmp}")
    private String myFilePathTmp;

    public static void main(String args[]){
        SpringApplication.run(WebApplication.class,args);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(WebApplication.class);
    }

    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        factory.setLocation(myFilePathTmp);//指定临时文件路径
        return factory.createMultipartConfig();
    }

    /**
     * 添加http的支持
     * 端口8012
     * */
    @Bean
    public ServletWebServerFactory servletContainer(){
        var tomcat = new TomcatServletWebServerFactory();
        var connector = new Connector("org.apache.coyote.http11.Http11NioProtocol");
        connector.setPort(8012);
        tomcat.addAdditionalTomcatConnectors(connector);
        return tomcat;
    }
}
