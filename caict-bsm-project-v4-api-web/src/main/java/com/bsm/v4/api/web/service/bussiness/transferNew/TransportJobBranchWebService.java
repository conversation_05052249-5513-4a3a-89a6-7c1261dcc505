package com.bsm.v4.api.web.service.bussiness.transferNew;

import com.bsm.v4.domain.security.service.business.transferNew.TransportJobBranchService;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 按地市拆分的子任务表WebService
 */
@Service
public class TransportJobBranchWebService extends BasicWebService {

    @Autowired
    private TransportJobBranchService transportJobBranchService;
    
}
