package com.bsm.v4.api.web.controller.business.transferNew;

import com.bsm.v4.api.web.service.bussiness.transferNew.TransportJobWebService;
import com.bsm.v4.api.web.service.security.AuthWebService;
import com.caictframework.data.controller.BasicController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 传输任务表Controller
 */
@RestController
@RequestMapping(value = "/apiWeb/transferNew/zhuanTransportJob")
@Api(value = "传输任务管理接口", tags = "传输任务管理接口")
public class TransportJobController extends BasicController {

    @Autowired
    private TransportJobWebService transportJobWebService;

    @Autowired
    private AuthWebService authWebService;



}
