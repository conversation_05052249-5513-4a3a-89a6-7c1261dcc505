package com.bsm.v4.api.web.service.security;

import com.bsm.v4.domain.security.service.security.DictionaryService;
import com.bsm.v4.system.model.dto.security.DictionaryDTO;
import com.bsm.v4.system.model.entity.security.Dictionary;
import com.bsm.v4.system.model.vo.security.DictionarySearchVO;
import com.bsm.v4.system.model.vo.security.DictionaryVO;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import com.caictframework.data.util.SnowflakeManager;
import com.caictframework.utils.util.TreeBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Title: DictionaryWebService
 * <AUTHOR>
 * @Package com.bsm.v4.api.web.service.security
 * @Date 2023/8/16 15:57
 * @description:
 */
@Service
public class DictionaryWebService extends BasicWebService {

    private static final Logger LOG = LoggerFactory.getLogger(DictionaryWebService.class);

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private SnowflakeManager snowflakeManager;

    /**
     * 添加、修改数据字典
     */
    public Map<String, Object> save(DictionaryVO dictionaryVO) {
        Dictionary dictionary = dictionaryService.findById(dictionaryVO.getId());
        int selectCountByCode = dictionaryService.selectCountByCode(dictionaryVO.getCode());

        if (dictionary != null) {
            if (!(dictionary.getCode()).equals(dictionaryVO.getCode()) && selectCountByCode > 0)
                return this.basicReturnFailure("error:-1");
        } else {
            if (selectCountByCode > 0) return this.basicReturnFailure("error:-1");
        }

        dictionary = new Dictionary();
        BeanUtils.copyProperties(dictionaryVO, dictionary);
        dictionary.setUpdateDate(new Date());
        return this.basicReturnResultJson(dictionaryService.save(dictionary));

    }

    /**
     * 批量添加
     */
    public Map<String, Object> insertBatch(List<DictionaryVO> dictionaryVOList) {
        List<Dictionary> dictionaryList = new ArrayList<Dictionary>();
        for (DictionaryVO dictionaryVO : dictionaryVOList) {
            Dictionary dictionary = new Dictionary();
            BeanUtils.copyProperties(dictionaryVO, dictionary);

            Long id = snowflakeManager.nextValue();
            dictionary.setId(id.toString());

            dictionaryList.add(dictionary);
        }
        return this.basicReturnResultJson(dictionaryService.insertBatch(dictionaryList));

    }

    /**
     * 批量删除
     */
    public Map<String, Object> deleteBatch(String[] ids) {
        List<Object> idList = new ArrayList<Object>();
        for (String id : ids) {
            idList.add(id);
        }
        return this.basicReturnResultJson(dictionaryService.deleteBatch(idList));
    }

    /**
     * 查询所有数据字典
     */
    public Map<String, Object> findAll() {
        List<Dictionary> dictionaryList = dictionaryService.findAll();
        List<DictionaryDTO> dictionaryDTOList = new ArrayList<DictionaryDTO>();
        if (dictionaryList != null) {
            for (Dictionary dictionary : dictionaryList) {
                DictionaryDTO dictionaryDTO = new DictionaryDTO();
                BeanUtils.copyProperties(dictionary, dictionaryDTO);
                dictionaryDTOList.add(dictionaryDTO);
            }
        }
        return this.basicReturnResultJson(dictionaryDTOList);
    }

    /**
     * 分页条件查询
     */
    public Map<String, Object> findAllPageByWhere(DictionarySearchVO dictionarySearchVO) {
        return this.basicReturnResultJson(new PageHandle(dictionarySearchVO).buildPage(findAllByWhere(dictionarySearchVO)));
    }

    /**
     * 条件查询
     */
    private List<DictionaryDTO> findAllByWhere(DictionarySearchVO dictionarySearchVO) {
        return dictionaryService.findAllByWhere(dictionarySearchVO);
    }

    /**
     * 查询所有数据字典(条件查询)
     */
    public Map<String, Object> findAllWhere(DictionarySearchVO dictionarySearchVO) {
        return this.basicReturnResultJson(dictionaryService.findAllDtoByWhere(dictionarySearchVO));
    }

    /**
     * 查询所有数据字典（树形结构）(条件查询)
     */
    public Map<String, Object> findAllTreeWhere(DictionarySearchVO dictionarySearchVO) {
        List<DictionaryDTO> dictionaryDTOList = dictionaryService.findAllDtoByWhere(dictionarySearchVO);
        if (dictionaryDTOList != null)
            return this.basicReturnResultJson(new TreeBuilder<DictionaryDTO>(dictionaryDTOList).buildTree());
        return null;
    }

    /**
     * 查询数据字典（根据id）
     */
    public Map<String, Object> findOneById(String id) {
        Dictionary dictionary = dictionaryService.findById(id);
        if (dictionary != null) {
            DictionaryDTO dictionaryDTO = new DictionaryDTO();
            BeanUtils.copyProperties(dictionary, dictionaryDTO);
            return this.basicReturnResultJson(dictionaryDTO);
        }
        return null;
    }

    /**
     * 查询所有数据字典（根据parentID)
     */
    public Map<String, Object> findAllByParentId(String parentId) {
        return this.basicReturnResultJson(dictionaryService.findAllByParentId(parentId));
    }

    /**
     * 查询所有数据字典（根据parentID)（树形结构）
     */
    public Map<String, Object> findTreeByParentId(String parentId) {
        List<DictionaryDTO> dictionaryDTOList = dictionaryService.findAllByParentId(parentId);
        if (dictionaryDTOList != null)
            return this.basicReturnResultJson(new TreeBuilder<DictionaryDTO>(dictionaryDTOList).buildTree(String.valueOf(parentId)));
        return null;
    }

    /**
     * 删除数据字典（根据id）
     */
    public Map<String, Object> delete(String id) {
        Dictionary dictionary = dictionaryService.findById(id);
        //修改子类父级
        dictionaryService.updateParent(id, dictionary.getParentId());
        //删除
        return this.basicReturnResultJson(dictionaryService.delete(id));
    }

    /**
     * 根据父级编号查询
     */
    public Map<String, Object> findAllByParentCode(String code) {
        return this.basicReturnResultJson(dictionaryService.findAllByParentCode(code));
    }

    /**
     * 根据id修改状态
     */
    public Map<String, Object> updateStatusById(String id, int status) {
        Dictionary dictionary = new Dictionary();
        dictionary.setId(id);
        dictionary.setStatus(status);
        return this.basicReturnResultJson(dictionaryService.update(dictionary));
    }
}
