package com.bsm.v4.api.web.service.bussiness.transferNew;

import com.bsm.v4.domain.security.service.business.transferNew.TransportRawBtsDealLogNewService;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 传输原始数据处理错误日志表WebService
 */
@Service
public class TransportRawBtsDealLogNewWebService extends BasicWebService {

    @Autowired
    private TransportRawBtsDealLogNewService transportRawBtsDealLogNewService;
    
}
