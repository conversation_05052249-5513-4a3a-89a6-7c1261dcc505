package com.bsm.v4.api.web.controller.business.transferNew;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.transferNew.ZhuanTransportJobWebService;
import com.bsm.v4.api.web.service.security.AuthWebService;
import com.bsm.v4.system.model.dto.business.transferNew.ZhuanTransportJobDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.vo.business.transferNew.ZhuanTransportJobVO;
import com.caictframework.data.controller.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 传输任务表Controller
 */
@RestController
@RequestMapping(value = "/apiWeb/transferNew/zhuanTransportJob")
@Api(value = "传输任务管理接口", tags = "传输任务管理接口")
public class zhuanTransportJobController extends BasicController {

    @Autowired
    private ZhuanTransportJobWebService zhuanTransportJobWebService;

    @Autowired
    private AuthWebService authWebService;



}
