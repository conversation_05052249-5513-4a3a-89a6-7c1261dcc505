package com.bsm.v4.api.web.controller.business.transferNew;

import com.bsm.v4.api.web.service.bussiness.transferNew.TransportRawBtsDealNewWebService;
import com.caictframework.data.controller.BasicController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * CSV 原始数据存储表Controller
 */
@RestController
@RequestMapping(value = "/apiWeb/transferNew/transportRawBtsDealNew")
@Api(value = "CSV 原始数据存储表管理接口", tags = "CSV 原始数据存储表管理接口")
public class TransportRawBtsDealNewController extends BasicController {

    @Autowired
    private TransportRawBtsDealNewWebService transportRawBtsDealNewWebService;
    
}
