package com.bsm.v4.api.web.service.bussiness.transferNew;

import com.bsm.v4.api.web.service.security.AuthWebService;
import com.bsm.v4.domain.security.service.business.transferNew.TransportJobService;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 传输任务表WebService
 */
@Service
public class TransportJobWebService extends BasicWebService {

    @Autowired
    private TransportJobService transportJobService;

    @Autowired
    private AuthWebService authWebService;

    @Value("${file.upload.path}")
    private String fileUploadPath;


}
