package com.bsm.v4.api.web.service.bussiness.transferNew;

import com.bsm.v4.api.web.service.security.AuthWebService;
import com.bsm.v4.domain.security.service.business.transferNew.TransportJobService;
import com.bsm.v4.system.model.vo.business.transferNew.FileUploadResultVO;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * 传输任务表WebService
 */
@Service
public class TransportJobWebService extends BasicWebService {

    @Value("${caict.myFilePath}")
    private String myFilePath;

    @Autowired
    private TransportJobService transportJobService;

    @Autowired
    private AuthWebService authWebService;

    /**
     * 上传CSV文件
     * @param file 上传的文件
     * @return 上传结果
     */
    public Map<String, Object> uploadCsvFile(MultipartFile file) {
        try {
            // 校验文件后缀名
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".csv")) {
                return this.basicReturnFailure("文件后缀名必须是.csv");
            }

            // 确保目录存在
            File uploadDir = new File(myFilePath);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }

            // 生成新文件名避免覆盖
            String newFileName = generateUniqueFileName(originalFilename);
            String filePath = myFilePath + File.separator + newFileName;

            // 保存文件
            File destFile = new File(filePath);
            file.transferTo(destFile);

            // 返回结果
            FileUploadResultVO result = new FileUploadResultVO(originalFilename, filePath);
            return this.basicReturnSuccess(result);

        } catch (IOException e) {
            return this.basicReturnFailure("文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            return this.basicReturnFailure("系统异常：" + e.getMessage());
        }
    }

    /**
     * 生成唯一文件名，避免文件覆盖
     * @param originalFilename 原始文件名
     * @return 新的唯一文件名
     */
    private String generateUniqueFileName(String originalFilename) {
        String baseName = originalFilename.substring(0, originalFilename.lastIndexOf('.'));
        String extension = originalFilename.substring(originalFilename.lastIndexOf('.'));

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String timestamp = sdf.format(new Date());

        String newFileName = baseName + "_" + timestamp + extension;

        // 检查文件是否存在，如果存在则添加序号
        File file = new File(myFilePath + File.separator + newFileName);
        int counter = 1;
        while (file.exists()) {
            newFileName = baseName + "_" + timestamp + "_" + counter + extension;
            file = new File(myFilePath + File.separator + newFileName);
            counter++;
        }

        return newFileName;
    }



}
