package com.bsm.v4.api.web.controller.business.transferNew;

import com.bsm.v4.api.web.service.bussiness.transferNew.TransportRawBtsDealLogNewWebService;
import com.caictframework.data.controller.BasicController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 传输原始数据处理错误日志表Controller
 */
@RestController
@RequestMapping(value = "/apiWeb/transferNew/transportRawBtsDealLogNew")
@Api(value = "传输原始数据处理错误日志管理接口", tags = "传输原始数据处理错误日志管理接口")
public class TransportRawBtsDealLogNewController extends BasicController {

    @Autowired
    private TransportRawBtsDealLogNewWebService transportRawBtsDealLogNewWebService;
    
}
