package com.bsm.v4.api.web.service.bussiness.transferNew;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.security.AuthWebService;
import com.bsm.v4.domain.security.service.business.transferNew.ZhuanTransportJobService;
import com.bsm.v4.system.model.dto.business.transferNew.ZhuanTransportJobDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.business.transferNew.ZhuanTransportJob;
import com.bsm.v4.system.model.vo.business.transferNew.ZhuanTransportJobVO;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import com.caictframework.utils.util.JSONResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;

/**
 * 传输任务表WebService
 */
@Service
public class ZhuanTransportJobWebService extends BasicWebService {

    @Autowired
    private ZhuanTransportJobService zhuanTransportJobService;

    @Autowired
    private AuthWebService authWebService;

    @Value("${file.upload.path}")
    private String fileUploadPath;


}
