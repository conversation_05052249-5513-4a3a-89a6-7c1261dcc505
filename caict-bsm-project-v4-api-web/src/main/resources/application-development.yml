server:
  port: 18011
#  ssl:
#    key-store: ${caict.myFilePath}caict.keystore
#    key-store-password: caict123456
#    key-store-type: JKS
  # 启用http2提速
  http2:
    enabled: true
  tomcat:
    basedir: D:/caict/bsm/tomcat

caict:
  myFilePath: D:/caict/bsm/4.0/file/
  myFilePathTmp: D:/caict/bsm/4.0/tmp/
  myFileExportPath: D:/caict/bsm/4.0/file/export/
  applyTemplate: bsm_apply_template.pdf
  myZipExportPath: D:/caict/bsm/4.0/file/zipTemp/
  myWordTemplateFile: D:/caict/bsm/4.0/file/export/
  myWordExportFile: D:/caict/bsm/4.0/file/export/report/
  mySqlInsertsNumber: 50
  mySqlDeleteNumber: 50
  myPasswordKey: caict
  zipPath: D:/caict/bsm/4.0/file/zip/
  generalTemplate: general_template.pdf
  generalTemplateForm: general_template_form.pdf
  message:
    url: 127.0.0.1
  replacePath: /bsm_4.0

spring:
  application:
    name: caict-bsm-4.0-web
  mvc:
    static-path-pattern: /**
    pathmatch:
      matching-strategy: ant_path_matcher
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************
    username: root
    password: Mysql@123
    validation-query: SELECT 1
    test-on-borrow: true
    max-active: 100
    max-idle: 8
    min-idle: 8
    initial-size: 10
    max-wait: 10000

  redis:
    database: 0
    host: 127.0.0.1
    port: 6379
    password:
    timeout: 10000
  web:
    resources:
      static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/,file:${caict.myFilePath}
  servlet:
    multipart:
      enabled: true
      max-file-size: 10240000000
      max-request-size: 102400000000
  main:
    allow-circular-references: true
  elasticsearch:
    uris: ************:9200

fdfs:
  so-timeout: 1500
  connect-timeout: 600
  tracker-list: *************:22122
hadoop:
  url: hdfs://************:8020
  namespace: /caict/bsm/4.0/file/
  user: root


mybatis:
  configuration:
    map-underscore-to-camel-case: true