<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.bsm.v4</groupId>
    <artifactId>caict-bsm-project-v4</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>

    <name>caict-bsm-project-v4</name>
    <description>无线电政务服务项目</description>

    <modules>
        <module>caict-bsm-project-v4-domain-security</module>
        <module>caict-bsm-project-v4-system-model</module>
        <module>caict-bsm-project-v4-api-web</module>
        <module>caict-bsm-project-v4-api-sync</module>
    </modules>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>2.7.0</version>
        <relativePath/>
    </parent>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>11</java.version>
        <caictframework-data.version>1.3-SNAPSHOT</caictframework-data.version>
        <caictframework-documnet.version>1.3-SNAPSHOT</caictframework-documnet.version>
        <domain-security.version>1.0-SNAPSHOT</domain-security.version>
        <system-model.version>1.0-SNAPSHOT</system-model.version>
        <caictframework-security.version>1.3-SNAPSHOT</caictframework-security.version>
    </properties>


    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.oracle.database.nls</groupId>
            <artifactId>orai18n</artifactId>
            <version>19.7.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.caictframework</groupId>
            <artifactId>caict-framework-data</artifactId>
            <version>${caictframework-data.version}</version>
        </dependency>
        <dependency>
            <groupId>org.caictframework</groupId>
            <artifactId>caict-framework-bigdata</artifactId>
            <version>${caictframework-data.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.caictframework</groupId>-->
<!--            <artifactId>caict-framework-security</artifactId>-->
<!--            <version>${caictframework-security.version}</version>-->
<!--        </dependency>-->
    </dependencies>
</project>