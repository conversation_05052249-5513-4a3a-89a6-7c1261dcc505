package com.bsm.v4.system.model.entity.security;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;

/**
 * Created by dengsy on 2021-8-13.
 * 用户信息对象
 */

@TableName("sys_users")
public class Users {

    @ApiModelProperty(value = "主键ID")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "编号")
    @TableFieId("code")
    private String code;

    @ApiModelProperty(value = "名称")
    @TableFieId("name")
    private String name;

    @ApiModelProperty(value = "头像")
    @TableFieId("img")
    private String img;

    @ApiModelProperty(value = "用户类型;数数据字典code")
    @TableFieId("type")
    private String type;

    @ApiModelProperty(value = "用户年龄")
    @TableFieId("age")
    private Integer age;

    @ApiModelProperty(value = "用户性别")
    @TableFieId("sex")
    private Integer sex;

    @ApiModelProperty(value = "联系方式")
    @TableFieId("phone")
    private String phone;

    @ApiModelProperty(value = "QQ")
    @TableFieId("qq")
    private String qq;

    @ApiModelProperty(value = "微信号")
    @TableFieId("wechat")
    private String wechat;

    @ApiModelProperty(value = "电子邮箱")
    @TableFieId("email")
    private String email;

    @ApiModelProperty(value = "保存时间")
    @TableFieId("update_date_time")
    private Date updateDateTime;

    @ApiModelProperty(value = "所属区域")
    @TableFieId("region_id")
    private String regionId;

    @ApiModelProperty(value = "状态")
    @TableFieId("status")
    private Integer status;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getQq() {
        return qq;
    }

    public void setQq(String qq) {
        this.qq = qq;
    }

    public String getWechat() {
        return wechat;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Date getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(Date updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
