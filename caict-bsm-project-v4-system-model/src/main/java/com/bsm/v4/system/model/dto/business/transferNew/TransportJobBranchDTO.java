package com.bsm.v4.system.model.dto.business.transferNew;

import com.bsm.v4.system.model.entity.business.transferNew.TransportJobBranch;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 按地市拆分的子任务表DTO
 */
public class TransportJobBranchDTO extends TransportJobBranch {

    /**
     * 开始时间（查询用）
     */
    @ApiModelProperty(value = "开始时间（查询用）")
    private Date startDate;

    /**
     * 结束时间（查询用）
     */
    @ApiModelProperty(value = "结束时间（查询用）")
    private Date endDate;

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int page;

    /**
     * 每页显示数量
     */
    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int rows;

    /**
     * 主任务信息（非数据库字段）
     */
    @ApiModelProperty(value = "主任务信息")
    private TransportJobDTO jobInfo;

    /**
     * 运营商代码（非数据库字段，来自主任务）
     */
    @ApiModelProperty(value = "运营商代码")
    private String operatorCode;

    /**
     * 申报类型（非数据库字段，来自主任务）
     */
    @ApiModelProperty(value = "申报类型")
    private String applyType;

    /**
     * 成功率（非数据库字段，计算得出）
     */
    @ApiModelProperty(value = "成功率")
    private Double successRate;

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public TransportJobDTO getJobInfo() {
        return jobInfo;
    }

    public void setJobInfo(TransportJobDTO jobInfo) {
        this.jobInfo = jobInfo;
    }

    public String getOperatorCode() {
        return operatorCode;
    }

    public void setOperatorCode(String operatorCode) {
        this.operatorCode = operatorCode;
    }

    public String getApplyType() {
        return applyType;
    }

    public void setApplyType(String applyType) {
        this.applyType = applyType;
    }

    public Double getSuccessRate() {
        if (getTotalCount() != null && getTotalCount() > 0 && getErrorCount() != null) {
            int successCount = getTotalCount() - getErrorCount();
            return (double) successCount / getTotalCount() * 100;
        }
        return null;
    }

    public void setSuccessRate(Double successRate) {
        // 只是为了兼容框架，实际值由计算得出
        // 不做任何操作
    }
}
