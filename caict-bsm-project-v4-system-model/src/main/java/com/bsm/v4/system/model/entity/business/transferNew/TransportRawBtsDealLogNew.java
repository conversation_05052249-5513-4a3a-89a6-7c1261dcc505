package com.bsm.v4.system.model.entity.business.transferNew;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 传输原始数据处理错误日志表实体类
 */
@TableName("TRANSPORT_RAW_BTS_DEAL_LOG_NEW")
public class TransportRawBtsDealLogNew {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @TableId("id")
    private Long id;

    /**
     * 所属任务ID
     */
    @ApiModelProperty(value = "所属任务ID")
    @TableFieId("job_id")
    private Long jobId;

    /**
     * 所属子任务ID
     */
    @ApiModelProperty(value = "所属子任务ID")
    @TableFieId("job_branch_id")
    private Long jobBranchId;

    /**
     * 出错字段名（国家标准字段）
     */
    @ApiModelProperty(value = "出错字段名（国家标准字段）")
    @TableFieId("field_name")
    private String fieldName;

    /**
     * 出错值原文
     */
    @ApiModelProperty(value = "出错值原文")
    @TableFieId("original_value")
    private String originalValue;

    /**
     * 错误类型（格式、缺失等）
     */
    @ApiModelProperty(value = "错误类型（格式、缺失等）")
    @TableFieId("error_type")
    private String errorType;

    /**
     * 错误说明
     */
    @ApiModelProperty(value = "错误说明")
    @TableFieId("error_message")
    private String errorMessage;

    /**
     * 整条出错数据（标准字段）
     */
    @ApiModelProperty(value = "整条出错数据（标准字段）")
    @TableFieId("deal_data")
    private String dealData;

    /**
     * 错误时间
     */
    @ApiModelProperty(value = "错误时间")
    @TableFieId("created_at")
    private Date createdAt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Long getJobBranchId() {
        return jobBranchId;
    }

    public void setJobBranchId(Long jobBranchId) {
        this.jobBranchId = jobBranchId;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getOriginalValue() {
        return originalValue;
    }

    public void setOriginalValue(String originalValue) {
        this.originalValue = originalValue;
    }

    public String getErrorType() {
        return errorType;
    }

    public void setErrorType(String errorType) {
        this.errorType = errorType;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getDealData() {
        return dealData;
    }

    public void setDealData(String dealData) {
        this.dealData = dealData;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }
}
