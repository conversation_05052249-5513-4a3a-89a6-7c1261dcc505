package com.bsm.v4.system.model.entity.business.transferNew;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 传输任务表实体类
 */
@TableName("ZHUAN_TRANSPORT_JOB")
public class ZhuanTransportJob {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @TableId("id")
    private Long id;

    /**
     * 运营商代码（如 CMCC、CUCC）
     */
    @ApiModelProperty(value = "运营商代码（如 CMCC、CUCC）")
    @TableFieId("operator_code")
    private String operatorCode;

    /**
     * 申报类型（全量、注销、续期）
     */
    @ApiModelProperty(value = "申报类型（全量、注销、续期）")
    @TableFieId("apply_type")
    private String applyType;

    /**
     * 任务表审核状态
     */
    @ApiModelProperty(value = "任务表审核状态：created-已创建，checking-校验中，invalid_data-数据错误，checked-校验通过，approving-审批中，approved-审批通过，rejected-审批驳回，done-完成")
    @TableFieId("status")
    private String status;

    /**
     * 上传的CSV文件路径
     */
    @ApiModelProperty(value = "上传的CSV文件路径")
    @TableFieId("csv_file_path")
    private String csvFilePath;

    /**
     * 创建人账号/ID
     */
    @ApiModelProperty(value = "创建人账号/ID")
    @TableFieId("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableFieId("created_at")
    private Date createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableFieId("updated_at")
    private Date updatedAt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOperatorCode() {
        return operatorCode;
    }

    public void setOperatorCode(String operatorCode) {
        this.operatorCode = operatorCode;
    }

    public String getApplyType() {
        return applyType;
    }

    public void setApplyType(String applyType) {
        this.applyType = applyType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCsvFilePath() {
        return csvFilePath;
    }

    public void setCsvFilePath(String csvFilePath) {
        this.csvFilePath = csvFilePath;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}
