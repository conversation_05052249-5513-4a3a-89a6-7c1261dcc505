package com.bsm.v4.system.model.entity.business.transferNew;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 按地市拆分的子任务表实体类
 */
@TableName("ZHUAN_TRANSPORT_JOB_BRANCH")
public class ZhuanTransportJobBranch {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @TableId("id")
    private Long id;

    /**
     * 所属主任务ID（外键）
     */
    @ApiModelProperty(value = "所属主任务ID（外键）")
    @TableFieId("job_id")
    private Long jobId;

    /**
     * 地市/地区名
     */
    @ApiModelProperty(value = "地市/地区名")
    @TableFieId("region_name")
    private String regionName;

    /**
     * 拆分任务状态
     */
    @ApiModelProperty(value = "拆分任务状态：created-已创建，checking-校验中，invalid_data-数据错误，checked-校验通过，approving-审批中，approved-审批通过，rejected-审批驳回，done-完成")
    @TableFieId("status")
    private String status;

    /**
     * 拆分后数据总数
     */
    @ApiModelProperty(value = "拆分后数据总数")
    @TableFieId("total_count")
    private Integer totalCount;

    /**
     * 拆分后错误条数
     */
    @ApiModelProperty(value = "拆分后错误条数")
    @TableFieId("error_count")
    private Integer errorCount;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableFieId("created_at")
    private Date createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableFieId("updated_at")
    private Date updatedAt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getErrorCount() {
        return errorCount;
    }

    public void setErrorCount(Integer errorCount) {
        this.errorCount = errorCount;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}
