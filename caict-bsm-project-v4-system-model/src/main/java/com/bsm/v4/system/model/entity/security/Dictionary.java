package com.bsm.v4.system.model.entity.security;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import com.caictframework.utils.annotation.TableParentId;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;

/**
 * Created by dengsy on 2021-8-13.
 * 数据字典对象
 */

@TableName("fsa_dictionary")
public class Dictionary {

    @ApiModelProperty(value = "主键ID")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "编号")
    @TableFieId("code")
    private String code;

    @ApiModelProperty(value = "名称")
    @TableFieId("name")
    private String name;

    @ApiModelProperty(value = "权重")
    @TableFieId("sort")
    private Integer sort;

    @ApiModelProperty(value = "类型")
    @TableFieId("type")
    private String type;

    @ApiModelProperty(value = "数据级别")
    @TableFieId("DICTIONARY_LEVEL")
    private Integer dictionaryLevel;

    @ApiModelProperty(value = "父级id")
    @TableFieId("parent_id")
    @TableParentId
    private String parentId;

    @ApiModelProperty(value = "保存时间")
    @TableFieId("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty(value = "状态;1：非必要；2：必要；3：停用")
    @TableFieId("status")
    private Integer status;

    @ApiModelProperty(value = "标准编号")
    @TableFieId("STANDARD_CODE")
    private String standardCode;

    @ApiModelProperty(value = "备注")
    @TableFieId("remark")
    private String remark;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getDictionaryLevel() {
        return dictionaryLevel;
    }

    public void setDictionaryLevel(Integer dictionaryLevel) {
        this.dictionaryLevel = dictionaryLevel;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStandardCode() {
        return standardCode;
    }

    public void setStandardCode(String standardCode) {
        this.standardCode = standardCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
