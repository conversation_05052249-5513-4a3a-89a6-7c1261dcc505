package com.bsm.v4.system.model.vo.business.transferNew;

import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 按地市拆分的子任务表VO
 */
@ApiModel(value = "zhuanTransportJobBranchVO", description = "按地市拆分的子任务表对象")
public class ZhuanTransportJobBranchVO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 所属主任务ID（外键）
     */
    @ApiModelProperty(value = "所属主任务ID（外键）")
    private Long jobId;

    /**
     * 地市/地区名
     */
    @ApiModelProperty(value = "地市/地区名")
    private String regionName;

    /**
     * 拆分任务状态
     */
    @ApiModelProperty(value = "拆分任务状态：created-已创建，checking-校验中，invalid_data-数据错误，checked-校验通过，approving-审批中，approved-审批通过，rejected-审批驳回，done-完成")
    private String status;

    /**
     * 拆分后数据总数
     */
    @ApiModelProperty(value = "拆分后数据总数")
    private Integer totalCount;

    /**
     * 拆分后错误条数
     */
    @ApiModelProperty(value = "拆分后错误条数")
    private Integer errorCount;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updatedAt;

    /**
     * 开始时间（查询用）
     */
    @ApiModelProperty(value = "开始时间（查询用）")
    private Date startDate;

    /**
     * 结束时间（查询用）
     */
    @ApiModelProperty(value = "结束时间（查询用）")
    private Date endDate;

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int page;

    /**
     * 每页显示数量
     */
    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int rows;

    /**
     * 令牌（用于认证）
     */
    @ApiModelProperty(value = "令牌（用于认证）")
    private String token;

    /**
     * 运营商代码（查询用，来自主任务）
     */
    @ApiModelProperty(value = "运营商代码（查询用）")
    private String operatorCode;

    /**
     * 申报类型（查询用，来自主任务）
     */
    @ApiModelProperty(value = "申报类型（查询用）")
    private String applyType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getErrorCount() {
        return errorCount;
    }

    public void setErrorCount(Integer errorCount) {
        this.errorCount = errorCount;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getOperatorCode() {
        return operatorCode;
    }

    public void setOperatorCode(String operatorCode) {
        this.operatorCode = operatorCode;
    }

    public String getApplyType() {
        return applyType;
    }

    public void setApplyType(String applyType) {
        this.applyType = applyType;
    }
}
