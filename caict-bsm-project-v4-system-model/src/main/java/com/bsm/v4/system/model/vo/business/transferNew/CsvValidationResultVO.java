package com.bsm.v4.system.model.vo.business.transferNew;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

/**
 * CSV校验结果VO
 */
@ApiModel(value = "csvValidationResultVO", description = "CSV校验结果对象")
public class CsvValidationResultVO {

    /**
     * 校验通过的数据行
     */
    @ApiModelProperty(value = "校验通过的数据行")
    private List<Map<String, Object>> validRows;

    /**
     * 校验失败的数据行
     */
    @ApiModelProperty(value = "校验失败的数据行")
    private List<Map<String, Object>> invalidRows;

    /**
     * 总行数
     */
    @ApiModelProperty(value = "总行数")
    private int totalRows;

    /**
     * 有效行数
     */
    @ApiModelProperty(value = "有效行数")
    private int validCount;

    /**
     * 无效行数
     */
    @ApiModelProperty(value = "无效行数")
    private int invalidCount;

    public CsvValidationResultVO() {
    }

    public CsvValidationResultVO(List<Map<String, Object>> validRows, List<Map<String, Object>> invalidRows) {
        this.validRows = validRows;
        this.invalidRows = invalidRows;
        this.totalRows = validRows.size() + invalidRows.size();
        this.validCount = validRows.size();
        this.invalidCount = invalidRows.size();
    }

    public List<Map<String, Object>> getValidRows() {
        return validRows;
    }

    public void setValidRows(List<Map<String, Object>> validRows) {
        this.validRows = validRows;
        updateCounts();
    }

    public List<Map<String, Object>> getInvalidRows() {
        return invalidRows;
    }

    public void setInvalidRows(List<Map<String, Object>> invalidRows) {
        this.invalidRows = invalidRows;
        updateCounts();
    }

    public int getTotalRows() {
        return totalRows;
    }

    public int getValidCount() {
        return validCount;
    }

    public int getInvalidCount() {
        return invalidCount;
    }

    private void updateCounts() {
        this.validCount = validRows != null ? validRows.size() : 0;
        this.invalidCount = invalidRows != null ? invalidRows.size() : 0;
        this.totalRows = this.validCount + this.invalidCount;
    }
}
