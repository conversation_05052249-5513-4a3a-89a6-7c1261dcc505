package com.bsm.v4.system.model.entity.security;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import com.caictframework.utils.annotation.TableParentId;
import io.swagger.annotations.ApiModelProperty;


/**
 * Created by dengsy on 2019-10-24.
 * 中国省市区对象
 */

@TableName("sys_region")
public class Region {

    @ApiModelProperty(value = "主键ID")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "编号")
    @TableFieId("code")
    private String code;

    @ApiModelProperty(value = "名字")
    @TableFieId("name")
    private String name;

    @ApiModelProperty(value = "父级id")
    @TableParentId
    @TableFieId("parent_id")
    private String parentId;

    @ApiModelProperty(value = "权重")
    @TableFieId("sort")
    private Integer sort;

    @ApiModelProperty(value = "区域详情")
    @TableFieId("region_details")
    private String regionDetails;

    @ApiModelProperty(value = "坐标集合")
    @TableFieId("coordinate")
    private String coordinate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getRegionDetails() {
        return regionDetails;
    }

    public void setRegionDetails(String regionDetails) {
        this.regionDetails = regionDetails;
    }

    public String getCoordinate() {
        return coordinate;
    }

    public void setCoordinate(String coordinate) {
        this.coordinate = coordinate;
    }
}
