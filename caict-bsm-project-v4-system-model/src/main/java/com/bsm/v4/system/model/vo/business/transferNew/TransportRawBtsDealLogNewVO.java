package com.bsm.v4.system.model.vo.business.transferNew;

import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 传输原始数据处理错误日志表VO
 */
@ApiModel(value = "transportRawBtsDealLogNewVO", description = "传输原始数据处理错误日志表对象")
public class TransportRawBtsDealLogNewVO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 所属任务ID
     */
    @ApiModelProperty(value = "所属任务ID")
    private Long jobId;

    /**
     * 所属子任务ID
     */
    @ApiModelProperty(value = "所属子任务ID")
    private Long jobBranchId;

    /**
     * 出错字段名（国家标准字段）
     */
    @ApiModelProperty(value = "出错字段名（国家标准字段）")
    private String fieldName;

    /**
     * 出错值原文
     */
    @ApiModelProperty(value = "出错值原文")
    private String originalValue;

    /**
     * 错误类型（格式、缺失等）
     */
    @ApiModelProperty(value = "错误类型（格式、缺失等）")
    private String errorType;

    /**
     * 错误说明
     */
    @ApiModelProperty(value = "错误说明")
    private String errorMessage;

    /**
     * 整条出错数据（标准字段）
     */
    @ApiModelProperty(value = "整条出错数据（标准字段）")
    private String dealData;

    /**
     * 错误时间
     */
    @ApiModelProperty(value = "错误时间")
    private Date createdAt;

    /**
     * 开始时间（查询用）
     */
    @ApiModelProperty(value = "开始时间（查询用）")
    private Date startDate;

    /**
     * 结束时间（查询用）
     */
    @ApiModelProperty(value = "结束时间（查询用）")
    private Date endDate;

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int page;

    /**
     * 每页显示数量
     */
    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int rows;

    /**
     * 令牌（用于认证）
     */
    @ApiModelProperty(value = "令牌（用于认证）")
    private String token;

    /**
     * 运营商代码（查询用，来自主任务）
     */
    @ApiModelProperty(value = "运营商代码（查询用）")
    private String operatorCode;

    /**
     * 申报类型（查询用，来自主任务）
     */
    @ApiModelProperty(value = "申报类型（查询用）")
    private String applyType;

    /**
     * 地区名称（查询用，来自子任务）
     */
    @ApiModelProperty(value = "地区名称（查询用）")
    private String regionName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Long getJobBranchId() {
        return jobBranchId;
    }

    public void setJobBranchId(Long jobBranchId) {
        this.jobBranchId = jobBranchId;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getOriginalValue() {
        return originalValue;
    }

    public void setOriginalValue(String originalValue) {
        this.originalValue = originalValue;
    }

    public String getErrorType() {
        return errorType;
    }

    public void setErrorType(String errorType) {
        this.errorType = errorType;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getDealData() {
        return dealData;
    }

    public void setDealData(String dealData) {
        this.dealData = dealData;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getOperatorCode() {
        return operatorCode;
    }

    public void setOperatorCode(String operatorCode) {
        this.operatorCode = operatorCode;
    }

    public String getApplyType() {
        return applyType;
    }

    public void setApplyType(String applyType) {
        this.applyType = applyType;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }
}
