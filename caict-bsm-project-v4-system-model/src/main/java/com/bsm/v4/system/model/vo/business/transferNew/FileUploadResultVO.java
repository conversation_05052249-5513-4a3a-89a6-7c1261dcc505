package com.bsm.v4.system.model.vo.business.transferNew;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 文件上传结果VO
 */
@ApiModel(value = "fileUploadResultVO", description = "文件上传结果对象")
public class FileUploadResultVO {

    /**
     * 原始文件名
     */
    @ApiModelProperty(value = "原始文件名")
    private String originalName;

    /**
     * 文件保存路径
     */
    @ApiModelProperty(value = "文件保存路径")
    private String filePath;

    public FileUploadResultVO() {
    }

    public FileUploadResultVO(String originalName, String filePath) {
        this.originalName = originalName;
        this.filePath = filePath;
    }

    public String getOriginalName() {
        return originalName;
    }

    public void setOriginalName(String originalName) {
        this.originalName = originalName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
}
