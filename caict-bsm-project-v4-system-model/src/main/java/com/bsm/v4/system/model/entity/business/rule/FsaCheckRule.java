package com.bsm.v4.system.model.entity.business.rule;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;

/**
 * Created by dsy62 on 2018-05-11.
 * 文件校验规则
 * id:id
 * orgGuid:组织机构guid
 * netTs:技术制式
 * genNum:信号
 * freqEfb:发射频率下限
 * freqEfe:发射频率上限
 * freqRfb:接收频率上限
 * freqRfe:接收频率下限
 * fileNo:频率使用许可证号或批准文号
 * state:状态，1：启用；2：停用
 */

@TableName("FSA_CHECK_RULE")
public class FsaCheckRule {

    @TableId(value = "id")
    private String id;
    @TableFieId("ORG_GUID")
    private String orgGuid;
    @TableFieId("NET_TS")
    private String netTs;
    @TableFieId("GEN_NUM")
    private String genNum;
    @TableFieId("FREQ_EFB")
    private Double freqEfb;
    @TableFieId("FREQ_EFE")
    private Double freqEfe;
    @TableFieId("FREQ_RFB")
    private Double freqRfb;
    @TableFieId("FREQ_RFE")
    private Double freqRfe;
    @TableFieId("FILE_NO")
    private String fileNo;
    @TableFieId("STATE")
    private String state;
    @TableFieId("ORG_TYPE")
    private String orgType;
    @TableFieId("EXPIRE_DATE")
    private Date expireDate;
    @TableFieId("START_DATE")
    private Date startDate;
    @TableFieId("END_DATE")
    private Date endDate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrgGuid() {
        return orgGuid;
    }

    public void setOrgGuid(String orgGuid) {
        this.orgGuid = orgGuid;
    }

    public String getNetTs() {
        return netTs;
    }

    public void setNetTs(String netTs) {
        this.netTs = netTs;
    }

    public String getGenNum() {
        return genNum;
    }

    public void setGenNum(String genNum) {
        this.genNum = genNum;
    }

    public Double getFreqEfb() {
        return freqEfb;
    }

    public void setFreqEfb(Double freqEfb) {
        this.freqEfb = freqEfb;
    }

    public Double getFreqEfe() {
        return freqEfe;
    }

    public void setFreqEfe(Double freqEfe) {
        this.freqEfe = freqEfe;
    }

    public Double getFreqRfb() {
        return freqRfb;
    }

    public void setFreqRfb(Double freqRfb) {
        this.freqRfb = freqRfb;
    }

    public Double getFreqRfe() {
        return freqRfe;
    }

    public void setFreqRfe(Double freqRfe) {
        this.freqRfe = freqRfe;
    }

    public String getFileNo() {
        return fileNo;
    }

    public void setFileNo(String fileNo) {
        this.fileNo = fileNo;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }
}
