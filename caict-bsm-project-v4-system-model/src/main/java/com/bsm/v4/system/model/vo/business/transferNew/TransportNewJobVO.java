package com.bsm.v4.system.model.vo.business.transferNew;

import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 传输任务表VO
 */
@ApiModel(value = "zhuanTransportJobVO", description = "传输任务表对象")
public class TransportNewJobVO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 运营商代码（如 CMCC、CUCC）
     */
    @ApiModelProperty(value = "运营商代码（如 CMCC、CUCC）")
    private String operatorCode;

    /**
     * 申报类型（全量、注销、续期）
     */
    @ApiModelProperty(value = "申报类型（全量、注销、续期）")
    private String applyType;

    /**
     * 任务表审核状态
     */
    @ApiModelProperty(value = "任务表审核状态：created-已创建，checking-校验中，invalid_data-数据错误，checked-校验通过，approving-审批中，approved-审批通过，rejected-审批驳回，done-完成")
    private String status;

    /**
     * 上传的CSV文件路径
     */
    @ApiModelProperty(value = "上传的CSV文件路径")
    private String csvFilePath;

    /**
     * 创建人账号/ID
     */
    @ApiModelProperty(value = "创建人账号/ID")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updatedAt;

    /**
     * 开始时间（查询用）
     */
    @ApiModelProperty(value = "开始时间（查询用）")
    private Date startDate;

    /**
     * 结束时间（查询用）
     */
    @ApiModelProperty(value = "结束时间（查询用）")
    private Date endDate;

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int page;

    /**
     * 每页显示数量
     */
    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int rows;

    /**
     * 文件内容（用于上传）
     */
    @ApiModelProperty(value = "文件内容（用于上传）")
    private String fileContent;

    /**
     * 文件名（用于上传）
     */
    @ApiModelProperty(value = "文件名（用于上传）")
    private String fileName;

    /**
     * 令牌（用于认证）
     */
    @ApiModelProperty(value = "令牌（用于认证）")
    private String token;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOperatorCode() {
        return operatorCode;
    }

    public void setOperatorCode(String operatorCode) {
        this.operatorCode = operatorCode;
    }

    public String getApplyType() {
        return applyType;
    }

    public void setApplyType(String applyType) {
        this.applyType = applyType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCsvFilePath() {
        return csvFilePath;
    }

    public void setCsvFilePath(String csvFilePath) {
        this.csvFilePath = csvFilePath;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public String getFileContent() {
        return fileContent;
    }

    public void setFileContent(String fileContent) {
        this.fileContent = fileContent;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}
