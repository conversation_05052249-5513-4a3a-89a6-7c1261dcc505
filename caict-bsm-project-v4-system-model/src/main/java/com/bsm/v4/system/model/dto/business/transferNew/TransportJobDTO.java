package com.bsm.v4.system.model.dto.business.transferNew;

import com.bsm.v4.system.model.entity.business.transferNew.TransportJob;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 传输任务表DTO
 */
public class TransportJobDTO extends TransportJob {

    /**
     * 开始时间（查询用）
     */
    @ApiModelProperty(value = "开始时间（查询用）")
    private Date startDate;

    /**
     * 结束时间（查询用）
     */
    @ApiModelProperty(value = "结束时间（查询用）")
    private Date endDate;

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int page;

    /**
     * 每页显示数量
     */
    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int rows;

    /**
     * 创建人姓名（非数据库字段）
     */
    @ApiModelProperty(value = "创建人姓名")
    private String createdByName;

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public String getCreatedByName() {
        return createdByName;
    }

    public void setCreatedByName(String createdByName) {
        this.createdByName = createdByName;
    }
}
