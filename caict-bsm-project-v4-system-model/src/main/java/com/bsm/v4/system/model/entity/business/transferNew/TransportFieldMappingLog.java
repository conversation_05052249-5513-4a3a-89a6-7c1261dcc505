package com.bsm.v4.system.model.entity.business.transferNew;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 字段映射记录表实体类（用于字段标准化跟踪）
 */
@TableName("TRANSPORT_FIELD_MAPPING_LOG")
public class TransportFieldMappingLog {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @TableId("id")
    private Long id;

    /**
     * 所属申报任务ID
     */
    @ApiModelProperty(value = "所属申报任务ID")
    @TableFieId("job_id")
    private Long jobId;

    /**
     * 所属子任务ID（可选）
     */
    @ApiModelProperty(value = "所属子任务ID（可选）")
    @TableFieId("job_branch_id")
    private Long jobBranchId;

    /**
     * 原始字段名（来自CSV）
     */
    @ApiModelProperty(value = "原始字段名（来自CSV）")
    @TableFieId("original_field_name")
    private String originalFieldName;

    /**
     * 映射后的标准字段名（用于系统字段）
     */
    @ApiModelProperty(value = "映射后的标准字段名（用于系统字段）")
    @TableFieId("mapped_field_name")
    private String mappedFieldName;

    /**
     * 记录创建时间
     */
    @ApiModelProperty(value = "记录创建时间")
    @TableFieId("create_time")
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Long getJobBranchId() {
        return jobBranchId;
    }

    public void setJobBranchId(Long jobBranchId) {
        this.jobBranchId = jobBranchId;
    }

    public String getOriginalFieldName() {
        return originalFieldName;
    }

    public void setOriginalFieldName(String originalFieldName) {
        this.originalFieldName = originalFieldName;
    }

    public String getMappedFieldName() {
        return mappedFieldName;
    }

    public void setMappedFieldName(String mappedFieldName) {
        this.mappedFieldName = mappedFieldName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
