package com.bsm.v4.system.model.vo.business.transferNew;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 作业元数据VO
 */
@ApiModel(value = "jobMetaVO", description = "作业元数据对象")
public class JobMetaVO {

    /**
     * 创建人账号/ID
     */
    @ApiModelProperty(value = "创建人账号/ID")
    private String createdBy;

    /**
     * 运营商代码（如 CMCC、CUCC）
     */
    @ApiModelProperty(value = "运营商代码（如 CMCC、CUCC）")
    private String operatorCode;

    /**
     * 申报类型（全量、注销、续期）
     */
    @ApiModelProperty(value = "申报类型（全量、注销、续期）")
    private String applyType;

    /**
     * 任务ID
     */
    @ApiModelProperty(value = "任务ID")
    private Long jobId;

    /**
     * 子任务ID
     */
    @ApiModelProperty(value = "子任务ID")
    private Long jobBranchId;

    public JobMetaVO() {
    }

    public JobMetaVO(String createdBy, String operatorCode, String applyType, Long jobId, Long jobBranchId) {
        this.createdBy = createdBy;
        this.operatorCode = operatorCode;
        this.applyType = applyType;
        this.jobId = jobId;
        this.jobBranchId = jobBranchId;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getOperatorCode() {
        return operatorCode;
    }

    public void setOperatorCode(String operatorCode) {
        this.operatorCode = operatorCode;
    }

    public String getApplyType() {
        return applyType;
    }

    public void setApplyType(String applyType) {
        this.applyType = applyType;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Long getJobBranchId() {
        return jobBranchId;
    }

    public void setJobBranchId(Long jobBranchId) {
        this.jobBranchId = jobBranchId;
    }
}
