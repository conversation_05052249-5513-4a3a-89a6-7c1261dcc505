package com.bsm.v4.system.model.vo.business.transferNew;

import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 字段映射记录表VO（用于字段标准化跟踪）
 */
@ApiModel(value = "transportFieldMappingLogVO", description = "字段映射记录表对象")
public class TransportFieldMappingLogVO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 所属申报任务ID
     */
    @ApiModelProperty(value = "所属申报任务ID")
    private Long jobId;

    /**
     * 所属子任务ID（可选）
     */
    @ApiModelProperty(value = "所属子任务ID（可选）")
    private Long jobBranchId;

    /**
     * 原始字段名（来自CSV）
     */
    @ApiModelProperty(value = "原始字段名（来自CSV）")
    private String originalFieldName;

    /**
     * 映射后的标准字段名（用于系统字段）
     */
    @ApiModelProperty(value = "映射后的标准字段名（用于系统字段）")
    private String mappedFieldName;

    /**
     * 记录创建时间
     */
    @ApiModelProperty(value = "记录创建时间")
    private Date createTime;

    /**
     * 开始时间（查询用）
     */
    @ApiModelProperty(value = "开始时间（查询用）")
    private Date startDate;

    /**
     * 结束时间（查询用）
     */
    @ApiModelProperty(value = "结束时间（查询用）")
    private Date endDate;

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int page;

    /**
     * 每页显示数量
     */
    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int rows;

    /**
     * 令牌（用于认证）
     */
    @ApiModelProperty(value = "令牌（用于认证）")
    private String token;

    /**
     * 运营商代码（查询用，来自主任务）
     */
    @ApiModelProperty(value = "运营商代码（查询用）")
    private String operatorCode;

    /**
     * 申报类型（查询用，来自主任务）
     */
    @ApiModelProperty(value = "申报类型（查询用）")
    private String applyType;

    /**
     * 地区名称（查询用，来自子任务）
     */
    @ApiModelProperty(value = "地区名称（查询用）")
    private String regionName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Long getJobBranchId() {
        return jobBranchId;
    }

    public void setJobBranchId(Long jobBranchId) {
        this.jobBranchId = jobBranchId;
    }

    public String getOriginalFieldName() {
        return originalFieldName;
    }

    public void setOriginalFieldName(String originalFieldName) {
        this.originalFieldName = originalFieldName;
    }

    public String getMappedFieldName() {
        return mappedFieldName;
    }

    public void setMappedFieldName(String mappedFieldName) {
        this.mappedFieldName = mappedFieldName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getOperatorCode() {
        return operatorCode;
    }

    public void setOperatorCode(String operatorCode) {
        this.operatorCode = operatorCode;
    }

    public String getApplyType() {
        return applyType;
    }

    public void setApplyType(String applyType) {
        this.applyType = applyType;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }
}
