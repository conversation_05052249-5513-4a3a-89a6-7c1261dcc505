package com.bsm.v4.system.model.vo.business.transferNew;

import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

/**
 * CSV 原始数据存储表VO
 */
@ApiModel(value = "transportRawBtsDealNewVO", description = "CSV 原始数据存储表对象")
public class TransportRawBtsDealNewVO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 所属申报任务ID
     */
    @ApiModelProperty(value = "所属申报任务ID")
    private Long jobId;

    /**
     * 所属子任务ID
     */
    @ApiModelProperty(value = "所属子任务ID")
    private Long jobBranchId;

    /**
     * 扇区名称
     */
    @ApiModelProperty(value = "扇区名称")
    private String cellName;

    /**
     * 扇区识别码（CELL_ID）
     */
    @ApiModelProperty(value = "扇区识别码（CELL_ID）")
    private String etEquCCode;

    /**
     * 台站名称（BTS_NAME）
     */
    @ApiModelProperty(value = "台站名称（BTS_NAME）")
    private String statName;

    /**
     * 台站识别码（BTS_ID）
     */
    @ApiModelProperty(value = "台站识别码（BTS_ID）")
    private String stCCode;

    /**
     * 技术体制（TECH_TYPE）
     */
    @ApiModelProperty(value = "技术体制（TECH_TYPE）")
    private String netTs;

    /**
     * 台址（LOCATION）
     */
    @ApiModelProperty(value = "台址（LOCATION）")
    private String statAddr;

    /**
     * 行政区（COUNTY）
     */
    @ApiModelProperty(value = "行政区（COUNTY）")
    private String county;

    /**
     * 经度（LONGITUDE）
     */
    @ApiModelProperty(value = "经度（LONGITUDE）")
    private BigDecimal statLg;

    /**
     * 纬度（LATITUDE）
     */
    @ApiModelProperty(value = "纬度（LATITUDE）")
    private BigDecimal statLa;

    /**
     * 发射起始频率（SEND_START_FREQ）
     */
    @ApiModelProperty(value = "发射起始频率（SEND_START_FREQ）")
    private BigDecimal freqEfe;

    /**
     * 发射终止频率（SEND_END_FREQ）
     */
    @ApiModelProperty(value = "发射终止频率（SEND_END_FREQ）")
    private BigDecimal freqEfb;

    /**
     * 接收起始频率（ACC_START_FREQ）
     */
    @ApiModelProperty(value = "接收起始频率（ACC_START_FREQ）")
    private BigDecimal freqRfb;

    /**
     * 接收终止频率（ACC_END_FREQ）
     */
    @ApiModelProperty(value = "接收终止频率（ACC_END_FREQ）")
    private BigDecimal freqRfe;

    /**
     * 最大发射功率（MAX_EMISSIVE_POWER）
     */
    @ApiModelProperty(value = "最大发射功率（MAX_EMISSIVE_POWER）")
    private BigDecimal maxEmissivePower;

    /**
     * 天线高度（HEIGHT）
     */
    @ApiModelProperty(value = "天线高度（HEIGHT）")
    private BigDecimal antHight;

    /**
     * 设备厂家（DEVICE_FACTORY）
     */
    @ApiModelProperty(value = "设备厂家（DEVICE_FACTORY）")
    private String equMenu;

    /**
     * 设备型号（DEVICE_MODEL）
     */
    @ApiModelProperty(value = "设备型号（DEVICE_MODEL）")
    private String equModel;

    /**
     * 型号核准代码（MODEL_CODE）
     */
    @ApiModelProperty(value = "型号核准代码（MODEL_CODE）")
    private String equAuth;

    /**
     * 天线类型（ANTENNA_MODEL）
     */
    @ApiModelProperty(value = "天线类型（ANTENNA_MODEL）")
    private String antType;

    /**
     * 天线生产厂家（ANTENNA_FACTORY）
     */
    @ApiModelProperty(value = "天线生产厂家（ANTENNA_FACTORY）")
    private String antMenu;

    /**
     * 极化方式（POLARIZATION_MODE）
     */
    @ApiModelProperty(value = "极化方式（POLARIZATION_MODE）")
    private String antPole;

    /**
     * 天线方位角（ANTENNA_AZIMUTH）
     */
    @ApiModelProperty(value = "天线方位角（ANTENNA_AZIMUTH）")
    private BigDecimal seComm;

    /**
     * 收倾角（AT_RANG）
     */
    @ApiModelProperty(value = "收倾角（AT_RANG）")
    private BigDecimal atRang;

    /**
     * 发倾角（AT_EANG）
     */
    @ApiModelProperty(value = "发倾角（AT_EANG）")
    private BigDecimal atEang;

    /**
     * 馈线系统总损耗（FEEDER_LOSS）
     */
    @ApiModelProperty(value = "馈线系统总损耗（FEEDER_LOSS）")
    private BigDecimal feedLose;

    /**
     * 天线增益（ANTENNA_GAIN）
     */
    @ApiModelProperty(value = "天线增益（ANTENNA_GAIN）")
    private BigDecimal antGain;

    /**
     * 海拔高度（ALTITUDE）
     */
    @ApiModelProperty(value = "海拔高度（ALTITUDE）")
    private BigDecimal statAt;

    /**
     * 设置年份（SET_YEAR）
     */
    @ApiModelProperty(value = "设置年份（SET_YEAR）")
    private Integer setYear;

    /**
     * 设置月份（SET_MONTH）
     */
    @ApiModelProperty(value = "设置月份（SET_MONTH）")
    private Integer setMonth;

    /**
     * 宏站/直放站（EXPAND_STATION）
     */
    @ApiModelProperty(value = "宏站/直放站（EXPAND_STATION）")
    private String expandStation;

    /**
     * 室外站/室内站（ATTRIBUTE_STATION）
     */
    @ApiModelProperty(value = "室外站/室内站（ATTRIBUTE_STATION）")
    private String attributeStation;

    /**
     * 服务半径（ST_SERV_R）
     */
    @ApiModelProperty(value = "服务半径（ST_SERV_R）")
    private BigDecimal stServR;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 开始时间（查询用）
     */
    @ApiModelProperty(value = "开始时间（查询用）")
    private Date startDate;

    /**
     * 结束时间（查询用）
     */
    @ApiModelProperty(value = "结束时间（查询用）")
    private Date endDate;

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int page;

    /**
     * 每页显示数量
     */
    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int rows;

    /**
     * 令牌（用于认证）
     */
    @ApiModelProperty(value = "令牌（用于认证）")
    private String token;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Long getJobBranchId() {
        return jobBranchId;
    }

    public void setJobBranchId(Long jobBranchId) {
        this.jobBranchId = jobBranchId;
    }

    public String getCellName() {
        return cellName;
    }

    public void setCellName(String cellName) {
        this.cellName = cellName;
    }

    public String getEtEquCCode() {
        return etEquCCode;
    }

    public void setEtEquCCode(String etEquCCode) {
        this.etEquCCode = etEquCCode;
    }

    public String getStatName() {
        return statName;
    }

    public void setStatName(String statName) {
        this.statName = statName;
    }

    public String getStCCode() {
        return stCCode;
    }

    public void setStCCode(String stCCode) {
        this.stCCode = stCCode;
    }

    public String getNetTs() {
        return netTs;
    }

    public void setNetTs(String netTs) {
        this.netTs = netTs;
    }

    public String getStatAddr() {
        return statAddr;
    }

    public void setStatAddr(String statAddr) {
        this.statAddr = statAddr;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public BigDecimal getStatLg() {
        return statLg;
    }

    public void setStatLg(BigDecimal statLg) {
        this.statLg = statLg;
    }

    public BigDecimal getStatLa() {
        return statLa;
    }

    public void setStatLa(BigDecimal statLa) {
        this.statLa = statLa;
    }

    public BigDecimal getFreqEfe() {
        return freqEfe;
    }

    public void setFreqEfe(BigDecimal freqEfe) {
        this.freqEfe = freqEfe;
    }

    public BigDecimal getFreqEfb() {
        return freqEfb;
    }

    public void setFreqEfb(BigDecimal freqEfb) {
        this.freqEfb = freqEfb;
    }

    public BigDecimal getFreqRfb() {
        return freqRfb;
    }

    public void setFreqRfb(BigDecimal freqRfb) {
        this.freqRfb = freqRfb;
    }

    public BigDecimal getFreqRfe() {
        return freqRfe;
    }

    public void setFreqRfe(BigDecimal freqRfe) {
        this.freqRfe = freqRfe;
    }

    public BigDecimal getMaxEmissivePower() {
        return maxEmissivePower;
    }

    public void setMaxEmissivePower(BigDecimal maxEmissivePower) {
        this.maxEmissivePower = maxEmissivePower;
    }

    public BigDecimal getAntHight() {
        return antHight;
    }

    public void setAntHight(BigDecimal antHight) {
        this.antHight = antHight;
    }

    public String getEquMenu() {
        return equMenu;
    }

    public void setEquMenu(String equMenu) {
        this.equMenu = equMenu;
    }

    public String getEquModel() {
        return equModel;
    }

    public void setEquModel(String equModel) {
        this.equModel = equModel;
    }

    public String getEquAuth() {
        return equAuth;
    }

    public void setEquAuth(String equAuth) {
        this.equAuth = equAuth;
    }

    public String getAntType() {
        return antType;
    }

    public void setAntType(String antType) {
        this.antType = antType;
    }

    public String getAntMenu() {
        return antMenu;
    }

    public void setAntMenu(String antMenu) {
        this.antMenu = antMenu;
    }

    public String getAntPole() {
        return antPole;
    }

    public void setAntPole(String antPole) {
        this.antPole = antPole;
    }

    public BigDecimal getSeComm() {
        return seComm;
    }

    public void setSeComm(BigDecimal seComm) {
        this.seComm = seComm;
    }

    public BigDecimal getAtRang() {
        return atRang;
    }

    public void setAtRang(BigDecimal atRang) {
        this.atRang = atRang;
    }

    public BigDecimal getAtEang() {
        return atEang;
    }

    public void setAtEang(BigDecimal atEang) {
        this.atEang = atEang;
    }

    public BigDecimal getFeedLose() {
        return feedLose;
    }

    public void setFeedLose(BigDecimal feedLose) {
        this.feedLose = feedLose;
    }

    public BigDecimal getAntGain() {
        return antGain;
    }

    public void setAntGain(BigDecimal antGain) {
        this.antGain = antGain;
    }

    public BigDecimal getStatAt() {
        return statAt;
    }

    public void setStatAt(BigDecimal statAt) {
        this.statAt = statAt;
    }

    public Integer getSetYear() {
        return setYear;
    }

    public void setSetYear(Integer setYear) {
        this.setYear = setYear;
    }

    public Integer getSetMonth() {
        return setMonth;
    }

    public void setSetMonth(Integer setMonth) {
        this.setMonth = setMonth;
    }

    public String getExpandStation() {
        return expandStation;
    }

    public void setExpandStation(String expandStation) {
        this.expandStation = expandStation;
    }

    public String getAttributeStation() {
        return attributeStation;
    }

    public void setAttributeStation(String attributeStation) {
        this.attributeStation = attributeStation;
    }

    public BigDecimal getStServR() {
        return stServR;
    }

    public void setStServR(BigDecimal stServR) {
        this.stServR = stServR;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}
