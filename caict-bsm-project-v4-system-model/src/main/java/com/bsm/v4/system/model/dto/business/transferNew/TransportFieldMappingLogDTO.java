package com.bsm.v4.system.model.dto.business.transferNew;

import com.bsm.v4.system.model.entity.business.transferNew.TransportFieldMappingLog;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 字段映射记录表DTO（用于字段标准化跟踪）
 */
public class TransportFieldMappingLogDTO extends TransportFieldMappingLog {

    /**
     * 开始时间（查询用）
     */
    @ApiModelProperty(value = "开始时间（查询用）")
    private Date startDate;

    /**
     * 结束时间（查询用）
     */
    @ApiModelProperty(value = "结束时间（查询用）")
    private Date endDate;

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int page;

    /**
     * 每页显示数量
     */
    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int rows;

    /**
     * 任务信息（非数据库字段）
     */
    @ApiModelProperty(value = "任务信息")
    private TransportJobNewDTO jobInfo;

    /**
     * 子任务信息（非数据库字段）
     */
    @ApiModelProperty(value = "子任务信息")
    private TransportJobBranchNewNewDTO branchInfo;

    /**
     * 运营商代码（非数据库字段，来自主任务）
     */
    @ApiModelProperty(value = "运营商代码")
    private String operatorCode;

    /**
     * 申报类型（非数据库字段，来自主任务）
     */
    @ApiModelProperty(value = "申报类型")
    private String applyType;

    /**
     * 地区名称（非数据库字段，来自子任务）
     */
    @ApiModelProperty(value = "地区名称")
    private String regionName;

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public TransportJobNewDTO getJobInfo() {
        return jobInfo;
    }

    public void setJobInfo(TransportJobNewDTO jobInfo) {
        this.jobInfo = jobInfo;
    }

    public TransportJobBranchNewNewDTO getBranchInfo() {
        return branchInfo;
    }

    public void setBranchInfo(TransportJobBranchNewNewDTO branchInfo) {
        this.branchInfo = branchInfo;
    }

    public String getOperatorCode() {
        return operatorCode;
    }

    public void setOperatorCode(String operatorCode) {
        this.operatorCode = operatorCode;
    }

    public String getApplyType() {
        return applyType;
    }

    public void setApplyType(String applyType) {
        this.applyType = applyType;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }
}
