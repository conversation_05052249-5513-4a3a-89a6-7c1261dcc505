package com.bsm.v4.domain.security.service.business.transferNew;

import com.bsm.v4.domain.security.mapper.business.transferNew.ZhuanTransportJobMapper;
import com.bsm.v4.system.model.dto.business.transferNew.ZhuanTransportJobDTO;
import com.bsm.v4.system.model.entity.business.transferNew.ZhuanTransportJob;
import com.bsm.v4.system.model.vo.business.transferNew.ZhuanTransportJobVO;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 传输任务表Service
 */
@Service
public class ZhuanTransportJobService extends BasicService<ZhuanTransportJob> {

    @Autowired
    private ZhuanTransportJobMapper zhuanTransportJobMapper;

}
