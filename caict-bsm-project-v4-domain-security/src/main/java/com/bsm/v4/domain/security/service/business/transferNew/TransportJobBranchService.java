package com.bsm.v4.domain.security.service.business.transferNew;

import com.bsm.v4.domain.security.mapper.business.transferNew.TransportJobBranchMapper;
import com.bsm.v4.system.model.entity.business.transferNew.TransportJobBranch;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 按地市拆分的子任务表Service
 */
@Service
public class TransportJobBranchService extends BasicService<TransportJobBranch> {

    @Autowired
    private TransportJobBranchMapper transportJobBranchMapper;
    
}
