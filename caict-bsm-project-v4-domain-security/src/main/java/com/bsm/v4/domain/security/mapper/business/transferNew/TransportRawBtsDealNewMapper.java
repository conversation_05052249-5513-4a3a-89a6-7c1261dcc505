package com.bsm.v4.domain.security.mapper.business.transferNew;

import com.bsm.v4.system.model.dto.business.transferNew.TransportRawBtsDealNewDTO;
import com.bsm.v4.system.model.entity.business.transferNew.TransportRawBtsDealNew;
import com.bsm.v4.system.model.vo.business.transferNew.TransportRawBtsDealNewVO;
import com.caictframework.data.mapper.BasicMapper;
import org.springframework.stereotype.Repository;

/**
 * CSV 原始数据存储表Mapper
 */
@Repository
public interface TransportRawBtsDealNewMapper extends BasicMapper<TransportRawBtsDealNew> {
    
}
