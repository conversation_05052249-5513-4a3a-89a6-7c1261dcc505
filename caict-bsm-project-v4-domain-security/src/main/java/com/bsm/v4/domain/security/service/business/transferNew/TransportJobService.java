package com.bsm.v4.domain.security.service.business.transferNew;

import com.bsm.v4.domain.security.mapper.business.transferNew.TransportJobMapper;
import com.bsm.v4.system.model.entity.business.transferNew.TransportJob;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 传输任务表Service
 */
@Service
public class TransportJobService extends BasicService<TransportJob> {

    @Autowired
    private TransportJobMapper transportJobMapper;

}
