package com.bsm.v4.domain.security.service.business.transferNew;

import com.bsm.v4.domain.security.mapper.business.transferNew.TransportRawBtsDealNewMapper;
import com.bsm.v4.system.model.entity.business.transferNew.TransportRawBtsDealNew;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * CSV 原始数据存储表Service
 */
@Service
public class TransportRawBtsDealNewService extends BasicService<TransportRawBtsDealNew> {

    @Autowired
    private TransportRawBtsDealNewMapper transportRawBtsDealNewMapper;
    
}
