package com.bsm.v4.domain.security.mapper.business.transferNew;

import com.bsm.v4.system.model.dto.business.transferNew.TransportRawBtsDealLogNewDTO;
import com.bsm.v4.system.model.entity.business.transferNew.TransportRawBtsDealLogNew;
import com.bsm.v4.system.model.vo.business.transferNew.TransportRawBtsDealLogNewVO;
import com.caictframework.data.mapper.BasicMapper;
import org.springframework.stereotype.Repository;

/**
 * 传输原始数据处理错误日志表Mapper
 */
@Repository
public interface TransportRawBtsDealLogNewMapper extends BasicMapper<TransportRawBtsDealLogNew> {
    
}
