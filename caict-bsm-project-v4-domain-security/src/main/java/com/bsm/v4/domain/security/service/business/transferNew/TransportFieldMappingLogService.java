package com.bsm.v4.domain.security.service.business.transferNew;

import com.bsm.v4.domain.security.mapper.business.transferNew.TransportFieldMappingLogMapper;
import com.bsm.v4.system.model.entity.business.transferNew.TransportFieldMappingLog;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 字段映射记录表Service（用于字段标准化跟踪）
 */
@Service
public class TransportFieldMappingLogService extends BasicService<TransportFieldMappingLog> {

    @Autowired
    private TransportFieldMappingLogMapper transportFieldMappingLogMapper;
    
}
