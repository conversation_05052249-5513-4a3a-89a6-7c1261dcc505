package com.bsm.v4.domain.security.service.business.transferNew;

import com.bsm.v4.domain.security.mapper.business.transferNew.TransportJobNewMapper;
import com.bsm.v4.system.model.entity.business.transferNew.TransportJobNew;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 传输任务表Service
 */
@Service
public class TransportJobNewService extends BasicService<TransportJobNew> {

    @Autowired
    private TransportJobNewMapper transportJobNewMapper;

}
