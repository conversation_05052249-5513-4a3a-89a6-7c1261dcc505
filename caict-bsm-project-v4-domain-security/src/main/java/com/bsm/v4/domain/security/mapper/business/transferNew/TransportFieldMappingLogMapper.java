package com.bsm.v4.domain.security.mapper.business.transferNew;

import com.bsm.v4.system.model.dto.business.transferNew.TransportFieldMappingLogDTO;
import com.bsm.v4.system.model.entity.business.transferNew.TransportFieldMappingLog;
import com.bsm.v4.system.model.vo.business.transferNew.TransportFieldMappingLogVO;
import com.caictframework.data.mapper.BasicMapper;
import org.springframework.stereotype.Repository;

/**
 * 字段映射记录表Mapper（用于字段标准化跟踪）
 */
@Repository
public interface TransportFieldMappingLogMapper extends BasicMapper<TransportFieldMappingLog> {
    
}
