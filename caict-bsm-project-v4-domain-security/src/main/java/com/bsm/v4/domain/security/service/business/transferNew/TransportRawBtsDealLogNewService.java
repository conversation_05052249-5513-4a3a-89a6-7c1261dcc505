package com.bsm.v4.domain.security.service.business.transferNew;

import com.bsm.v4.domain.security.mapper.business.transferNew.TransportRawBtsDealLogNewMapper;
import com.bsm.v4.system.model.entity.business.transferNew.TransportRawBtsDealLogNew;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 传输原始数据处理错误日志表Service
 */
@Service
public class TransportRawBtsDealLogNewService extends BasicService<TransportRawBtsDealLogNew> {

    @Autowired
    private TransportRawBtsDealLogNewMapper transportRawBtsDealLogNewMapper;
    
}
